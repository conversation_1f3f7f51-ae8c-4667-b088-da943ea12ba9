{"root": "/Volumes/Farrukh/React Projects/khan_baba", "reactNativePath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native", "reactNativeVersion": "0.76", "dependencies": {"@react-native-async-storage/async-storage": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-async-storage/async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-async-storage/async-storage/RNCAsyncStorage.podspec", "version": "2.2.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-async-storage/async-storage/android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "@react-native-community/push-notification-ios": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-community/push-notification-ios", "name": "@react-native-community/push-notification-ios", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-community/push-notification-ios/RNCPushNotificationIOS.podspec", "version": "1.11.0", "configurations": [], "scriptPhases": []}, "android": null}}, "@react-native-picker/picker": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-picker/picker", "name": "@react-native-picker/picker", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-picker/picker/RNCPicker.podspec", "version": "2.11.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-picker/picker/android", "packageImportPath": "import com.reactnativecommunity.picker.RNCPickerPackage;", "packageInstance": "new RNCPickerPackage()", "buildTypes": [], "libraryName": "rnpicker", "componentDescriptors": ["RNCAndroidDialogPickerComponentDescriptor", "RNCAndroidDropdownPickerComponentDescriptor"], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/@react-native-picker/picker/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-config": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-config", "name": "react-native-config", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-config/react-native-config.podspec", "version": "1.5.5", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-config/android", "packageImportPath": "import com.lugg.RNCConfig.RNCConfigPackage;", "packageInstance": "new RNCConfigPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-config/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-device-info": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-device-info", "name": "react-native-device-info", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-device-info/RNDeviceInfo.podspec", "version": "14.0.4", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-device-info/android", "packageImportPath": "import com.learnium.RNDeviceInfo.RNDeviceInfo;", "packageInstance": "new RNDeviceInfo()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-device-info/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-gesture-handler": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-gesture-handler/RNGestureHandler.podspec", "version": "2.25.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-gesture-handler/android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerButtonComponentDescriptor", "RNGestureHandlerRootViewComponentDescriptor"], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-haptic-feedback": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-haptic-feedback", "name": "react-native-haptic-feedback", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-haptic-feedback/RNReactNativeHapticFeedback.podspec", "version": "2.3.3", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-haptic-feedback/android", "packageImportPath": "import com.mkuczera.RNReactNativeHapticFeedbackPackage;", "packageInstance": "new RNReactNativeHapticFeedbackPackage()", "buildTypes": [], "libraryName": "RNHapticFeedbackSpec", "componentDescriptors": [], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-haptic-feedback/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-pager-view": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-pager-view", "name": "react-native-pager-view", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-pager-view/react-native-pager-view.podspec", "version": "6.8.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-pager-view/android", "packageImportPath": "import com.reactnativepagerview.PagerViewPackage;", "packageInstance": "new PagerViewPackage()", "buildTypes": [], "libraryName": "pagerview", "componentDescriptors": ["RNCViewPagerComponentDescriptor"], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-pager-view/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-push-notification": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-push-notification", "name": "react-native-push-notification", "platforms": {"ios": null, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-push-notification/android", "packageImportPath": "import com.dieam.reactnativepushnotification.ReactNativePushNotificationPackage;", "packageInstance": "new ReactNativePushNotificationPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-push-notification/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-reanimated": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-reanimated", "name": "react-native-reanimated", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-reanimated/RNReanimated.podspec", "version": "3.17.5", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-reanimated/android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-safe-area-context": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-safe-area-context/react-native-safe-area-context.podspec", "version": "4.14.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-safe-area-context/android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-screens": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-screens", "name": "react-native-screens", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-screens/RNScreens.podspec", "version": "4.11.1", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-screens/android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-splash-screen": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-splash-screen", "name": "react-native-splash-screen", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-splash-screen/react-native-splash-screen.podspec", "version": "3.3.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-splash-screen/android", "packageImportPath": "import org.devio.rn.splashscreen.SplashScreenReactPackage;", "packageInstance": "new SplashScreenReactPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-splash-screen/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-vector-icons": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-vector-icons", "name": "react-native-vector-icons", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-vector-icons/RNVectorIcons.podspec", "version": "10.2.0", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-vector-icons/android", "packageImportPath": "import com.oblador.vectoricons.VectorIconsPackage;", "packageInstance": "new VectorIconsPackage()", "buildTypes": [], "libraryName": "RNVectorIconsSpec", "componentDescriptors": [], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-vector-icons/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}, "react-native-webview": {"root": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-webview", "name": "react-native-webview", "platforms": {"ios": {"podspecPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-webview/react-native-webview.podspec", "version": "13.13.5", "configurations": [], "scriptPhases": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-webview/android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "/Volumes/Farrukh/React Projects/khan_baba/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null, "isPureCxxDependency": false}}}}, "commands": [{"name": "bundle", "description": "Build the bundle for the provided JavaScript entry file.", "options": [{"name": "--entry-file <path>", "description": "Path to the root JS file, either absolute or relative to JS root"}, {"name": "--platform <string>", "description": "Either \"ios\" or \"android\"", "default": "ios"}, {"name": "--transformer <string>", "description": "Specify a custom transformer to be used"}, {"name": "--dev [boolean]", "description": "If false, warnings are disabled and the bundle is minified", "default": true}, {"name": "--minify [boolean]", "description": "Allows overriding whether bundle is minified. This defaults to false if dev is true, and true if dev is false. Disabling minification can be useful for speeding up production builds for testing purposes."}, {"name": "--bundle-output <string>", "description": "File name where to store the resulting bundle, ex. /tmp/groups.bundle"}, {"name": "--bundle-encoding <string>", "description": "Encoding the bundle should be written in (https://nodejs.org/api/buffer.html#buffer_buffer).", "default": "utf8"}, {"name": "--max-workers <number>", "description": "Specifies the maximum number of workers the worker-pool will spawn for transforming files. This defaults to the number of the cores available on your machine."}, {"name": "--sourcemap-output <string>", "description": "File name where to store the sourcemap file for resulting bundle, ex. /tmp/groups.map"}, {"name": "--sourcemap-sources-root <string>", "description": "Path to make sourcemap's sources entries relative to, ex. /root/dir"}, {"name": "--sourcemap-use-absolute-path", "description": "Report SourceMapURL using its full path", "default": false}, {"name": "--assets-dest <string>", "description": "Directory name where to store assets referenced in the bundle"}, {"name": "--unstable-transform-profile <string>", "description": "Experimental, transform JS for a specific JS engine. Currently supported: hermes, hermes-canary, default", "default": "default"}, {"name": "--asset-catalog-dest [string]", "description": "Path where to create an iOS Asset Catalog for images"}, {"name": "--reset-cache", "description": "Removes cached files", "default": false}, {"name": "--read-global-cache", "description": "Try to fetch transformed JS code from the global cache, if configured.", "default": false}, {"name": "--config <string>", "description": "Path to the CLI configuration file"}, {"name": "--resolver-option <string...>", "description": "Custom resolver options of the form key=value. URL-encoded. May be specified multiple times."}]}, {"name": "start", "description": "Start the React Native development server.", "options": [{"name": "--port <number>"}, {"name": "--host <string>", "default": ""}, {"name": "--projectRoot <path>", "description": "Path to a custom project root"}, {"name": "--watchFolders <list>", "description": "Specify any additional folders to be added to the watch list"}, {"name": "--assetPlugins <list>", "description": "Specify any additional asset plugins to be used by the packager by full filepath"}, {"name": "--sourceExts <list>", "description": "Specify any additional source extensions to be used by the packager"}, {"name": "--max-workers <number>", "description": "Specifies the maximum number of workers the worker-pool will spawn for transforming files. This defaults to the number of the cores available on your machine."}, {"name": "--transformer <string>", "description": "Specify a custom transformer to be used"}, {"name": "--reset-cache, --resetCache", "description": "Removes cached files"}, {"name": "--custom-log-reporter-path, --customLogReporterPath <string>", "description": "Path to a JavaScript file that exports a log reporter as a replacement for TerminalReporter"}, {"name": "--https", "description": "Enables https connections to the server"}, {"name": "--key <path>", "description": "Path to custom SSL key"}, {"name": "--cert <path>", "description": "Path to custom SSL cert"}, {"name": "--config <string>", "description": "Path to the CLI configuration file"}, {"name": "--no-interactive", "description": "Disables interactive mode"}]}, {"name": "codegen", "options": [{"name": "--path <path>", "description": "Path to the React Native project root.", "default": "/Volumes/Farrukh/React Projects/khan_baba"}, {"name": "--platform <string>", "description": "Target platform. Supported values: \"android\", \"ios\", \"all\".", "default": "all"}, {"name": "--outputPath <path>", "description": "Path where generated artifacts will be output to."}]}, {"name": "log-ios", "description": "starts iOS device syslog tail", "options": [{"name": "-i --interactive", "description": "Explicitly select simulator to tail logs from. By default it will tail logs from the first booted and available simulator."}]}, {"name": "run-ios", "description": "builds your app and starts it on iOS simulator", "examples": [{"desc": "Run on a different simulator, e.g. iPhone SE (2nd generation)", "cmd": "npx react-native run-ios --simulator \"iPhone SE (2nd generation)\""}, {"desc": "Run on a connected device, e.g. <PERSON>'s iPhone", "cmd": "npx react-native run-ios --device \"Max's iPhone\""}, {"desc": "Run on the AppleTV simulator", "cmd": "npx react-native run-ios --simulator \"Apple TV\"  --scheme \"helloworld-tvOS\""}], "options": [{"name": "--no-packager", "description": "Do not launch packager while running the app"}, {"name": "--port <number>", "default": 8081}, {"name": "--terminal <string>", "description": "Launches the Metro Bundler in a new window using the specified terminal path.", "default": "vscode"}, {"name": "--binary-path <string>", "description": "Path relative to project root where pre-built .app binary lives."}, {"name": "--list-devices", "description": "List all available iOS devices and simulators and let you choose one to run the app. "}, {"name": "--udid <string>", "description": "Explicitly set the device to use by UDID"}, {"name": "--simulator <string>", "description": "Explicitly set the simulator to use. Optionally set the iOS version between parentheses at the end to match an exact version: \"iPhone 15 (17.0)\""}, {"name": "--mode <string>", "description": "Explicitly set the scheme configuration to use. This option is case sensitive."}, {"name": "--scheme <string>", "description": "Explicitly set Xcode scheme to use"}, {"name": "--destination <string>", "description": "Explicitly extend destination e.g. \"arch=x86_64\""}, {"name": "--verbose", "description": "Do not use xcbeautify or xcpretty even if installed"}, {"name": "--xcconfig [string]", "description": "Explicitly set xcconfig to use"}, {"name": "--buildFolder <string>", "description": "Location for iOS build artifacts. Corresponds to Xcode's \"-derivedDataPath\"."}, {"name": "--extra-params <string>", "description": "Custom params that will be passed to xcodebuild command."}, {"name": "--target <string>", "description": "Explicitly set Xcode target to use."}, {"name": "-i --interactive", "description": "Explicitly select which scheme and configuration to use before running a build"}, {"name": "--force-pods", "description": "Force CocoaPods installation"}, {"name": "--device [string]", "description": "Explicitly set the device to use by name or by unique device identifier . If the value is not provided,the app will run on the first available physical device."}]}, {"name": "build-ios", "description": "builds your app for iOS platform", "examples": [{"desc": "Build the app for all iOS devices in Release mode", "cmd": "npx react-native build-ios --mode \"Release\""}], "options": [{"name": "--mode <string>", "description": "Explicitly set the scheme configuration to use. This option is case sensitive."}, {"name": "--scheme <string>", "description": "Explicitly set Xcode scheme to use"}, {"name": "--destination <string>", "description": "Explicitly extend destination e.g. \"arch=x86_64\""}, {"name": "--verbose", "description": "Do not use xcbeautify or xcpretty even if installed"}, {"name": "--xcconfig [string]", "description": "Explicitly set xcconfig to use"}, {"name": "--buildFolder <string>", "description": "Location for iOS build artifacts. Corresponds to Xcode's \"-derivedDataPath\"."}, {"name": "--extra-params <string>", "description": "Custom params that will be passed to xcodebuild command."}, {"name": "--target <string>", "description": "Explicitly set Xcode target to use."}, {"name": "-i --interactive", "description": "Explicitly select which scheme and configuration to use before running a build"}, {"name": "--force-pods", "description": "Force CocoaPods installation"}, {"name": "--device [string]", "description": "Explicitly set the device to use by name or by unique device identifier . If the value is not provided,the app will run on the first available physical device."}]}, {"name": "log-android", "description": "starts logki<PERSON>"}, {"name": "run-android", "description": "builds your app and starts it on a connected Android emulator or device", "options": [{"name": "--mode <string>", "description": "Specify your app's build variant"}, {"name": "--tasks <list>", "description": "Run custom Gradle tasks. By default it's \"assembleDebug\". Will override passed mode and variant arguments."}, {"name": "--active-arch-only", "description": "Build native libraries only for the current device architecture for debug builds.", "default": false}, {"name": "--extra-params <string>", "description": "Custom params passed to gradle build command"}, {"name": "-i --interactive", "description": "Explicitly select build type and flavour to use before running a build"}, {"name": "--no-packager", "description": "Do not launch packager while running the app"}, {"name": "--port <number>", "default": 8081}, {"name": "--terminal <string>", "description": "Launches the Metro Bundler in a new window using the specified terminal path.", "default": "vscode"}, {"name": "--appId <string>", "description": "Specify an applicationId to launch after build. If not specified, `package` from AndroidManifest.xml will be used.", "default": ""}, {"name": "--appIdSuffix <string>", "description": "Specify an applicationIdSuffix to launch after build.", "default": ""}, {"name": "--main-activity <string>", "description": "Name of the activity to start"}, {"name": "--device <string>", "description": "Explicitly set the device to use by name. The value is not required if you have a single device connected."}, {"name": "--deviceId <string>", "description": "**DEPRECATED** Builds your app and starts it on a specific device/simulator with the given device id (listed by running \"adb devices\" on the command line)."}, {"name": "--list-devices", "description": "Lists all available Android devices and simulators and let you choose one to run the app", "default": false}, {"name": "--binary-path <string>", "description": "Path relative to project root where pre-built .apk binary lives."}, {"name": "--user <number>", "description": "Id of the User Profile you want to install the app on."}]}, {"name": "build-android", "description": "builds your app", "options": [{"name": "--mode <string>", "description": "Specify your app's build variant"}, {"name": "--tasks <list>", "description": "Run custom Gradle tasks. By default it's \"assembleDebug\". Will override passed mode and variant arguments."}, {"name": "--active-arch-only", "description": "Build native libraries only for the current device architecture for debug builds.", "default": false}, {"name": "--extra-params <string>", "description": "Custom params passed to gradle build command"}, {"name": "-i --interactive", "description": "Explicitly select build type and flavour to use before running a build"}]}], "healthChecks": [], "platforms": {"ios": {}, "android": {}}, "assets": ["./src/assets/fonts/"], "project": {"ios": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/ios", "xcodeProject": {"name": "KhanBaba.xcworkspace", "path": ".", "isWorkspace": true}, "assets": []}, "android": {"sourceDir": "/Volumes/Farrukh/React Projects/khan_baba/android", "appName": "app", "packageName": "com.ordrz.khanbaba", "applicationId": "com.ordrz.khanbaba", "mainActivity": ".MainActivity", "assets": []}}}