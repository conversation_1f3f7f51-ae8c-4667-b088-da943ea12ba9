{"configurations": [{"directories": [{"build": ".", "jsonFile": "directory-.-Debug-f5ebdc15457944623624.json", "minimumCMakeVersion": {"string": "3.13"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "appmodules", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "appmodules::@6890427a1f51a3e7e1df", "jsonFile": "target-appmodules-Debug-5f3b06ed11af50a2abce.json", "name": "appmodules", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/Volumes/Zafeer/react_apps/AwesomeProject/android/app/.cxx/Debug/5ga315a5/armeabi-v7a", "source": "/Volumes/Zafeer/react_apps/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup"}, "version": {"major": 2, "minor": 3}}