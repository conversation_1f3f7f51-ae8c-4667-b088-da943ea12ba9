{"buildFiles": ["/Volumes/Zafeer/react_apps/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/CMakeLists.txt"], "cleanCommandsComponents": [["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/Zafeer/react_apps/AwesomeProject/android/app/.cxx/Debug/5ga315a5/x86", "clean"]], "buildTargetsCommandComponents": ["/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja", "-C", "/Volumes/Zafeer/react_apps/AwesomeProject/android/app/.cxx/Debug/5ga315a5/x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {"appmodules::@6890427a1f51a3e7e1df": {"toolchain": "toolchain", "abi": "x86", "artifactName": "appmodules", "output": "/Volumes/Zafeer/react_apps/AwesomeProject/android/app/build/intermediates/cxx/Debug/5ga315a5/obj/x86/libappmodules.so", "runtimeFiles": ["/Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/libs/android.x86/libfbjni.so", "/Users/<USER>/.gradle/caches/8.13/transforms/16ecd3d905ee83882a5c0ce77c8c6dc3/transformed/react-android-0.79.3-debug/prefab/modules/jsi/libs/android.x86/libjsi.so", "/Users/<USER>/.gradle/caches/8.13/transforms/16ecd3d905ee83882a5c0ce77c8c6dc3/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/libs/android.x86/libreactnative.so"]}}, "toolchains": {"toolchain": {"cCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang.lld", "cppCompilerExecutable": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++.lld"}}, "cFileExtensions": [], "cppFileExtensions": ["cpp"]}