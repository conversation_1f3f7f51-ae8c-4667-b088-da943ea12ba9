[{"directory": "/Volumes/Zafeer/react_apps/AwesomeProject/android/app/.cxx/Debug/5ga315a5/x86", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=i686-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Volumes/Zafeer/react_apps/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Volumes/Zafeer/react_apps/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -isystem /Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/16ecd3d905ee83882a5c0ce77c8c6dc3/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/16ecd3d905ee83882a5c0ce77c8c6dc3/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -o CMakeFiles/appmodules.dir/Volumes/Zafeer/react_apps/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp.o -c /Volumes/Zafeer/react_apps/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp", "file": "/Volumes/Zafeer/react_apps/AwesomeProject/android/app/build/generated/autolinking/src/main/jni/autolinking.cpp"}, {"directory": "/Volumes/Zafeer/react_apps/AwesomeProject/android/app/.cxx/Debug/5ga315a5/x86", "command": "/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/bin/clang++ --target=i686-none-linux-android24 --sysroot=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/darwin-x86_64/sysroot -Dappmodules_EXPORTS -I/Volumes/Zafeer/react_apps/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -I/Volumes/Zafeer/react_apps/AwesomeProject/android/app/build/generated/autolinking/src/main/jni -isystem /Users/<USER>/.gradle/caches/8.13/transforms/72cde7dc85b5006383f56c98fcfedfa5/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/16ecd3d905ee83882a5c0ce77c8c6dc3/transformed/react-android-0.79.3-debug/prefab/modules/jsi/include -isystem /Users/<USER>/.gradle/caches/8.13/transforms/16ecd3d905ee83882a5c0ce77c8c6dc3/transformed/react-android-0.79.3-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -o CMakeFiles/appmodules.dir/OnLoad.cpp.o -c /Volumes/Zafeer/react_apps/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp", "file": "/Volumes/Zafeer/react_apps/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup/OnLoad.cpp"}]