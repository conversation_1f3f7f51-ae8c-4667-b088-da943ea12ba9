                        -H/Volumes/Zafeer/react_apps/AwesomeProject/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup
-DCMAKE_SYSTEM_NAME=Android
-DCMAKE_EXPORT_COMPILE_COMMANDS=ON
-DCMAKE_SYSTEM_VERSION=24
-DANDROID_PLATFORM=android-24
-DANDROID_ABI=x86
-DCMAKE_ANDROID_ARCH_ABI=x86
-DANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006
-DCMAKE_ANDROID_NDK=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006
-DCMAKE_TOOLCHAIN_FILE=/Users/<USER>/Library/Android/sdk/ndk/27.1.12297006/build/cmake/android.toolchain.cmake
-DCMAKE_MAKE_PROGRAM=/Users/<USER>/Library/Android/sdk/cmake/3.22.1/bin/ninja
-DCMA<PERSON>_LIBRARY_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/AwesomeProject/android/app/build/intermediates/cxx/Debug/5ga315a5/obj/x86
-DCMAKE_RUNTIME_OUTPUT_DIRECTORY=/Volumes/Zafeer/react_apps/AwesomeProject/android/app/build/intermediates/cxx/Debug/5ga315a5/obj/x86
-DCMAKE_BUILD_TYPE=Debug
-DCMAKE_FIND_ROOT_PATH=/Volumes/Zafeer/react_apps/AwesomeProject/android/app/.cxx/Debug/5ga315a5/prefab/x86/prefab
-B/Volumes/Zafeer/react_apps/AwesomeProject/android/app/.cxx/Debug/5ga315a5/x86
-GNinja
-DPROJECT_BUILD_DIR=/Volumes/Zafeer/react_apps/AwesomeProject/android/app/build
-DPROJECT_ROOT_DIR=/Volumes/Zafeer/react_apps/AwesomeProject/android
-DREACT_ANDROID_DIR=/Volumes/Zafeer/react_apps/AwesomeProject/node_modules/react-native/ReactAndroid
-DANDROID_STL=c++_shared
-DANDROID_SUPPORT_FLEXIBLE_PAGE_SIZES=ON
                        Build command args: []
                        Version: 2